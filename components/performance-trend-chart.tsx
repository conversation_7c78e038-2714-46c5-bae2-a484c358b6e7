"use client"

import { CHART_COLORS, formatCurrency, formatDate, formatNumber } from "@/lib/chart-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip as Recharts<PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON>s,
  YAxis,
} from "recharts"
import React, { useEffect, useState } from "react"

import { Skeleton } from "./ui/skeleton"
import { useCampaignData } from "@/lib/api/campaign-data-service"
import { useFilters } from "@/lib/contexts/filter-context"

// Data structure for the chart
interface TrendChartDataPoint {
  date: string;
  total_spend: number | null;
  conversions: number | null;
  roas: number | null;
}

// Define the metrics we want to display
interface MetricConfig {
  id: string;
  name: string;
  dataKey: string;
  formatType: 'currency' | 'number' | 'ratio';
  color: string;
  description: string;
}

const METRICS: MetricConfig[] = [
  {
    id: 'spend',
    name: 'Total Spend',
    dataKey: 'total_spend',
    formatType: 'currency',
    color: CHART_COLORS.blue,
    description: 'Total advertising spend over time'
  },
  {
    id: 'conversions',
    name: 'Conversions',
    dataKey: 'conversions',
    formatType: 'number',
    color: CHART_COLORS.green,
    description: 'Total conversions over time'
  },
  {
    id: 'roas',
    name: 'ROAS',
    dataKey: 'roas',
    formatType: 'ratio',
    color: CHART_COLORS.orange,
    description: 'Return on Ad Spend over time'
  }
];

export function PerformanceTrendChart() {
  const { filters, getQueryParams } = useFilters()
  const queryParamsString = getQueryParams().toString()
  const { data: campaignData, loading, error: apiError } = useCampaignData(queryParamsString)

  const [chartData, setChartData] = useState<TrendChartDataPoint[]>([])
  const [error, setError] = useState<string | null>(null)
  const currentCurrency = filters.currency

  // Process campaign data when it's loaded
  useEffect(() => {
    if (loading) {
      // Reset chart data while loading
      setChartData([])
      setError(null)
      return
    }

    if (apiError) {
      console.error("API Error:", apiError)
      setError(apiError)
      setChartData([])
      return
    }

    try {
      if (!Array.isArray(campaignData) || campaignData.length === 0) {
        console.warn("No campaign data returned from API or data is not an array.")
        setChartData([])
        return
      }

      // Process data into TrendChartDataPoint[]
      const processedData = campaignData.map(item => {
        const totalSpend = item.totalSpend ?? 0;
        const conversions = item.totalConversions ?? 0;
        // ROAS might be directly from item.roas or recalculated if necessary
        const roas = item.roas ?? (totalSpend > 0 ? (item.totalConversionValue ?? 0) / totalSpend : 0);

        return {
          date: item.date || '',
          total_spend: totalSpend,
          conversions: conversions,
          roas: roas,
        }
      }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      setChartData(processedData)
    } catch (err) {
      console.error("Failed to process trend chart data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      setChartData([])
    }
  }, [campaignData, loading, apiError])

  // Format values for tooltips and Y-axis
  const formatValue = (value: number, formatType: 'currency' | 'number' | 'ratio') => {
    switch (formatType) {
      case 'currency':
        return formatCurrency(value, currentCurrency);
      case 'number':
        return formatNumber(value);
      case 'ratio':
        return `${value.toFixed(2)}x`;
      default:
        return value.toString();
    }
  };

  const rechartsDateFormatter = (dateString: string) => {
    return formatDate(dateString);
  };

  const rechartsLabelFormatter = (dateString: string) => {
    return formatDate(dateString);
  };


  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Spend, Conversions, and ROAS over time.</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Error loading trend data: {error}</p>
        </CardContent>
      </Card>
    )
  }

  if (!chartData.length && !loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Spend, Conversions, and ROAS over time.</CardDescription>
        </CardHeader>
        <CardContent className="h-[400px] flex items-center justify-center">
          <p className="text-muted-foreground">No trend data available for the selected filters.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Trends</CardTitle>
        <CardDescription>
          Spend, Conversions, and ROAS over time (Aggregation: {filters.groupBy})
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
            barCategoryGap="15%"
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
            <XAxis
              dataKey="date"
              tickFormatter={rechartsDateFormatter}
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={11}
              interval={0}
              tick={{ fontSize: 11 }}
            />
            <YAxis
              yAxisId="left"
              orientation="left"
              tickFormatter={(value) => {
                return new Intl.NumberFormat("en-CA", {
                  notation: "compact",
                  style: "currency",
                  currency: currentCurrency,
                  maximumFractionDigits: 1,
                }).format(value);
              }}
              tick={{ fontSize: 11 }}
              width={80}
              stroke={CHART_COLORS.blue}
            />
            <YAxis
              yAxisId="right"
              orientation="right"
              tickFormatter={(value) => {
                return new Intl.NumberFormat("en-CA", {
                  notation: "compact",
                  maximumFractionDigits: 0,
                }).format(value);
              }}
              tick={{ fontSize: 11 }}
              width={60}
              stroke={CHART_COLORS.green}
            />
            <RechartsTooltip
              formatter={(value: number, name: string) => {
                if (name === "Total Spend") return [formatCurrency(value, currentCurrency), name];
                if (name === "Conversions") return [formatNumber(value), name];
                if (name === "ROAS") return [`${value.toFixed(2)}x`, name];
                return [value.toString(), name];
              }}
              labelFormatter={rechartsLabelFormatter}
            />
            <Bar
              yAxisId="left"
              dataKey="total_spend"
              name="Total Spend"
              fill={CHART_COLORS.blue}
              opacity={0.8}
            />
            <Bar
              yAxisId="right"
              dataKey="conversions"
              name="Conversions"
              fill={CHART_COLORS.green}
              opacity={0.8}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
