"use client"

import { CHART_COLORS, formatCurrency, formatDate, formatNumber } from "@/lib/chart-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip as Recharts<PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  YAxis,
} from "recharts"
import React, { useEffect, useState } from "react"

import { Skeleton } from "./ui/skeleton"
import { useCampaignData } from "@/lib/api/campaign-data-service"
import { useFilters } from "@/lib/contexts/filter-context"

// Data structure for the chart
interface TrendChartDataPoint {
  date: string;
  total_spend: number | null;
  conversions: number | null;
  roas: number | null;
}

// Define the metrics we want to display - matching KPI card style
interface MetricConfig {
  id: string;
  name: string;
  dataKey: string;
  formatType: 'currency' | 'number' | 'ratio';
  description: string;
}

const METRICS: MetricConfig[] = [
  {
    id: 'spend',
    name: 'Total Spend',
    dataKey: 'total_spend',
    formatType: 'currency',
    description: 'Total advertising spend over time (Aggregation: month)'
  },
  {
    id: 'conversions',
    name: 'Conversions',
    dataKey: 'conversions',
    formatType: 'number',
    description: 'Total conversions over time (Aggregation: month)'
  },
  {
    id: 'roas',
    name: 'ROAS',
    dataKey: 'roas',
    formatType: 'ratio',
    description: 'Return on Ad Spend over time (Aggregation: month)'
  }
];

export function PerformanceTrendChart() {
  const { filters, getQueryParams } = useFilters()
  const queryParamsString = getQueryParams().toString()
  const { data: campaignData, loading, error: apiError } = useCampaignData(queryParamsString)

  const [chartData, setChartData] = useState<TrendChartDataPoint[]>([])
  const [error, setError] = useState<string | null>(null)
  const currentCurrency = filters.currency

  // Process campaign data when it's loaded
  useEffect(() => {
    if (loading) {
      setChartData([])
      setError(null)
      return
    }

    if (apiError) {
      console.error("API Error:", apiError)
      setError(apiError)
      setChartData([])
      return
    }

    try {
      if (!Array.isArray(campaignData) || campaignData.length === 0) {
        console.warn("No campaign data returned from API or data is not an array.")
        setChartData([])
        return
      }

      // Process data into TrendChartDataPoint[]
      const processedData = campaignData.map(item => {
        const totalSpend = item.totalSpend ?? 0;
        const conversions = item.totalConversions ?? 0;
        const roas = item.roas ?? (totalSpend > 0 ? (item.totalConversionValue ?? 0) / totalSpend : 0);

        return {
          date: item.date || '',
          total_spend: totalSpend,
          conversions: conversions,
          roas: roas,
        }
      }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      setChartData(processedData)
    } catch (err) {
      console.error("Failed to process trend chart data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      setChartData([])
    }
  }, [campaignData, loading, apiError])

  // Format date for chart display
  const rechartsDateFormatter = (dateString: string) => {
    return formatDate(dateString);
  };

  const rechartsLabelFormatter = (dateString: string) => {
    return formatDate(dateString);
  };

  // Custom tooltip formatter matching KPI cards
  const customTooltipFormatter = (value: number, name: string, props: any) => {
    const metric = METRICS.find(m => m.dataKey === name);
    if (!metric) return [value.toString(), name];

    switch (metric.formatType) {
      case 'currency':
        return [formatCurrency(value, currentCurrency), metric.name];
      case 'number':
        return [formatNumber(value), metric.name];
      case 'ratio':
        return [`${value.toFixed(2)}x`, metric.name];
      default:
        return [value.toString(), metric.name];
    }
  };


  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
        {METRICS.map((metric) => (
          <Card key={metric.id}>
            <CardHeader>
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              <CardDescription className="text-xs">{metric.description}</CardDescription>
            </CardHeader>
            <CardContent className="h-[250px]">
              <Skeleton className="h-full w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
        {METRICS.map((metric) => (
          <Card key={metric.id}>
            <CardHeader>
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-500 text-sm">Error loading data: {error}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!chartData.length && !loading) {
    return (
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
        {METRICS.map((metric) => (
          <Card key={metric.id}>
            <CardHeader>
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              <CardDescription className="text-xs">{metric.description}</CardDescription>
            </CardHeader>
            <CardContent className="h-[250px] flex items-center justify-center">
              <p className="text-muted-foreground text-sm">No data available</p>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
      {METRICS.map((metric) => (
        <Card key={metric.id}>
          <CardHeader>
            <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
            <CardDescription className="text-xs">
              {metric.description} (Aggregation: {filters.groupBy})
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[250px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 10, right: 10, left: 10, bottom: 40 }}
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                <XAxis
                  dataKey="date"
                  tickFormatter={rechartsDateFormatter}
                  angle={-45}
                  textAnchor="end"
                  height={50}
                  fontSize={10}
                  interval={0}
                  tick={{ fontSize: 10 }}
                />
                <YAxis
                  tickFormatter={(value) => {
                    switch (metric.formatType) {
                      case 'currency':
                        return new Intl.NumberFormat("en-CA", {
                          notation: "compact",
                          style: "currency",
                          currency: currentCurrency,
                          maximumFractionDigits: 1,
                        }).format(value);
                      case 'ratio':
                        return `${value.toFixed(1)}x`;
                      default:
                        return new Intl.NumberFormat("en-CA", {
                          notation: "compact",
                          maximumFractionDigits: 0,
                        }).format(value);
                    }
                  }}
                  tick={{ fontSize: 10 }}
                  width={metric.formatType === 'currency' ? 70 : 50}
                />
                <RechartsTooltip
                  formatter={(value: number) => [formatValue(value, metric.formatType), metric.name]}
                  labelFormatter={rechartsLabelFormatter}
                />
                <Bar
                  dataKey={metric.dataKey}
                  fill={metric.color}
                  radius={[2, 2, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
