"use client";

// Import from the new src/lib location
import { FilterProvider, GroupingOption, useFilters } from "@/lib/contexts/filter-context";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"

import { AppLayout } from "@/components/shared/AppLayout"
import { CampaignDataTable } from "@/components/campaign-data-table"
import { EnhancedDashboardFilters } from "@/components/enhanced-dashboard-filters"
import { OverallKPICards } from "@/components/marketing-kpi-cards"
import { PerformanceTrendChart } from "@/components/performance-trend-chart"
import { SpendBreakdownChart } from "@/components/spend-breakdown-chart"

// Create a wrapper component that uses the FilterProvider
function MarketingDashboardContent() {
  const { filters, setFilters } = useFilters();

  return (
    <>
      {/* Data Aggregation Controls - Moved to top */}
      <div className="flex justify-start items-center space-x-2 my-4">
        <span className="text-sm font-medium">Aggregate Data By:</span>
        <ToggleGroup
          type="single"
          value={filters.groupBy}
          onValueChange={(value) => {
            if (value) {
              setFilters(prev => ({ ...prev, groupBy: value as GroupingOption }));
            }
          }}
          aria-label="Data aggregation period"
        >
          <ToggleGroupItem value="day" aria-label="Daily">Daily</ToggleGroupItem>
          <ToggleGroupItem value="week" aria-label="Weekly">Weekly</ToggleGroupItem>
          <ToggleGroupItem value="month" aria-label="Monthly">Monthly</ToggleGroupItem>
        </ToggleGroup>
      </div>

      {/* Dashboard Filters */}
      <EnhancedDashboardFilters
        showCountryFilter={false}
        showCurrencyFilter={false} // Currency is usually global or per-user, not a typical dashboard filter here
        showGroupByFilter={false} // GroupBy is handled by the aggregation toggle above
        pageType="marketing-dashboard"
      />

      {/* KPI Summary Section */}
      <OverallKPICards />

      {/* Performance Trends Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Performance Trends</h3>
        <PerformanceTrendChart />
      </div>

      {/* Spend Breakdown Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Spend Breakdown</h3>
        <SpendBreakdownChart />
      </div>

      {/* Main Data Table Section */}
      <CampaignDataTable />
    </>
  );
}

// Main component that provides the context
export default function MarketingDashboardClient() {
  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Marketing Dashboard"
      pageDescription="Track and analyze your marketing performance"
      showPageHeader={true}
    >
      <FilterProvider>
        <MarketingDashboardContent />
      </FilterProvider>
    </AppLayout>
  )
}